import { useState, useEffect, useMemo } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from 'react-router-dom';
import { FaStar, FaThumbsUp, FaThumbsDown, FaComment, FaArrowLeft, FaChevronDown, FaChevronUp, FaExclamationTriangle, FaDownload, FaLock, FaGamepad, FaTools, FaFileAlt, FaLink, FaTimes, FaImage, FaPlayCircle, FaTrash, FaExclamationCircle, FaCheck, FaUnlock, FaExpand, FaCompress, FaHeart } from 'react-icons/fa';
import { getGameById, getGameBySlug, getGameReviews, submitGameReview, deleteGameReview, sendReviewReaction, getReviewComments, submitReviewComment, likeGame, dislikeGame, toggleFavoriteGame, getGameInteractions } from '../services/gameService';
import { useAuth } from '../context/AuthContext';
import { useLanguage } from '../context/LanguageContext';
import LoadingSpinner from '../components/LoadingSpinner';
import { gamePlaceholder, screenshotPlaceholder, avatarPlaceholder } from '../assets/placeholders';
import { BASE_URL, API_URL } from '../config/env.js';
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import PropTypes from 'prop-types';

// CSS styles are now handled with Tailwind CSS classes
import PurchaseModal from '../components/PurchaseModal';

// New WebGameEmbed component with improved URL handling
const WebGameEmbed = ({ game, onError }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [debugInfo, setDebugInfo] = useState({});
  const [gameStarted, setGameStarted] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showExitMessage, setShowExitMessage] = useState(false);
  const { t } = useLanguage();

  // Memoize the embed URL calculation to prevent infinite re-renders
  const { embedUrl, debugData } = useMemo(() => {
    let embedUrl = null;
    let debugData = {};

    // Priority 1: If there's a direct web_game_url (from S3 extracted files), use it
    if (game.web_game_url) {
      debugData.source = 'web_game_url';
      debugData.url = game.web_game_url;
      return { embedUrl: game.web_game_url, debugData };
    }

    // Priority 2: Special case for legacy Unity game (ID 7) - keep for backward compatibility
    if (game.id === 7) {
      const baseUrl = BASE_URL;
      // Use the relay.html page instead of index.html
      const unityGamePath = '/uploads/games/7/files/2dplatformerweb/relay.html';
      embedUrl = `${baseUrl}${unityGamePath}`;
      debugData.source = 'legacy_unity_game';
      debugData.url = embedUrl;
      return { embedUrl, debugData };
    }
    
    // Priority 3: Fallback to legacy embedded web files (for backward compatibility)
    // Note: New uploads use S3 extraction and set web_game_url, so this is mainly for old games
    if (game.has_embedded_version && game.files) {
      const webPlayableFile = game.files.find(file => 
        file.is_web_playable && !file.requires_purchase
      );
      
      if (webPlayableFile) {
        // Use the centralized base URL configuration
        const baseUrl = BASE_URL;
        debugData.baseUrl = baseUrl;
        
        // Get the file path and ensure proper formatting
        let filePath = webPlayableFile.filePath || '';
        debugData.originalFilePath = filePath;
        
        // Make sure the file path starts with a slash
        if (!filePath.startsWith('/')) {
          filePath = `/${filePath}`;
        }
        debugData.normalizedFilePath = filePath;

        // For 2dplatformerweb special case or Unity web builds
        if (filePath.includes('2dplatformerweb') || webPlayableFile.file_name?.includes('2dplatformerweb')) {
          // For Unity builds, we need to point directly to the entry point HTML file
          const entryPoint = webPlayableFile.web_entry_point || 'index.html';
          debugData.entryPoint = entryPoint;
          
          // If it's a ZIP file, we need to construct the path to the extracted content
          let webPath = filePath;
          if (webPlayableFile.file_name?.endsWith('.zip')) {
            // For ZIP files, assume they're extracted to a folder with the same name (without .zip)
            const fileName = webPlayableFile.file_name.replace('.zip', '');
            webPath = `/uploads/games/${game.id}/files/${fileName}`;
          }
          
          // Construct the final URL: baseUrl + webPath + entry point
          // Make sure we don't double-up on slashes
          if (webPath.endsWith('/')) {
            embedUrl = `${baseUrl}${webPath}${entryPoint}`;
          } else {
            embedUrl = `${baseUrl}${webPath}/${entryPoint}`;
          }
          debugData.constructedUrl = embedUrl;
        } else {
          // For other web files, construct the path to the directory
          const pathParts = filePath.split('/');
          
          // Check if the last part is a file or a directory
          // If it looks like a file (has an extension), remove it to get the directory
          const lastPart = pathParts[pathParts.length - 1];
          if (lastPart.includes('.') && !lastPart.endsWith('/')) {
            pathParts.pop(); // Remove the file part
          }
          
          let basePath = `${baseUrl}${pathParts.join('/')}`;
          if (!basePath.endsWith('/')) {
            basePath += '/';
          }
          debugData.basePath = basePath;
          
          // Append the web entry point
          const entryPoint = webPlayableFile.web_entry_point || '';
          debugData.entryPoint = entryPoint;
          
          if (entryPoint) {
            embedUrl = `${basePath}${entryPoint}`;
            debugData.constructedUrl = embedUrl;
          }
        }
      }
    }
    
    return { embedUrl, debugData };
  }, [game.id, game.web_game_url, game.has_embedded_version, game.files]);

  // Update debug info when it changes
  useEffect(() => {
    setDebugInfo(debugData);
  }, [debugData]);
  
  const handleLoad = () => {
    setIsLoading(false);
  };
  
  const handleError = () => {
    console.error("Failed to load game from URL:", embedUrl, debugInfo);
    setIsLoading(false);
    setError("Failed to load web game");
    if (onError) onError();
  };

  // Fullscreen functionality
  const enterFullscreen = async (element) => {
    try {
      if (element.requestFullscreen) {
        await element.requestFullscreen();
      } else if (element.webkitRequestFullscreen) {
        await element.webkitRequestFullscreen();
      } else if (element.mozRequestFullScreen) {
        await element.mozRequestFullScreen();
      } else if (element.msRequestFullscreen) {
        await element.msRequestFullscreen();
      }
    } catch (error) {
      console.error('Error entering fullscreen:', error);
    }
  };

  const exitFullscreen = async () => {
    try {
      if (document.exitFullscreen) {
        await document.exitFullscreen();
      } else if (document.webkitExitFullscreen) {
        await document.webkitExitFullscreen();
      } else if (document.mozCancelFullScreen) {
        await document.mozCancelFullScreen();
      } else if (document.msExitFullscreen) {
        await document.msExitFullscreen();
      }
    } catch (error) {
      console.error('Error exiting fullscreen:', error);
    }
  };

  const toggleFullscreen = async () => {
    const gameContainer = document.getElementById(`game-container-${game.id}`);
    if (!gameContainer) return;

    if (isFullscreen) {
      await exitFullscreen();
    } else {
      await enterFullscreen(gameContainer);
    }
  };

  // Listen for fullscreen changes
  useEffect(() => {
    const handleFullscreenChange = () => {
      const isCurrentlyFullscreen = !!(
        document.fullscreenElement ||
        document.webkitFullscreenElement ||
        document.mozFullScreenElement ||
        document.msFullscreenElement
      );

      setIsFullscreen(isCurrentlyFullscreen);

      if (isCurrentlyFullscreen && gameStarted) {
        // Show exit message for 5 seconds when entering fullscreen
        setShowExitMessage(true);
        setTimeout(() => setShowExitMessage(false), 5000);
      }
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.addEventListener('mozfullscreenchange', handleFullscreenChange);
    document.addEventListener('MSFullscreenChange', handleFullscreenChange);

    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
      document.removeEventListener('mozfullscreenchange', handleFullscreenChange);
      document.removeEventListener('MSFullscreenChange', handleFullscreenChange);
    };
  }, [gameStarted]);

  const handlePlayClick = async () => {
    setGameStarted(true);

    // Automatically enter fullscreen when play is clicked
    setTimeout(async () => {
      const gameContainer = document.getElementById(`game-container-${game.id}`);
      if (gameContainer) {
        await enterFullscreen(gameContainer);
      }
    }, 100); // Small delay to ensure the iframe is rendered
  };
  
  if (!embedUrl) {
    return (
      <div className="flex flex-col items-center justify-center p-8 bg-gray-800 rounded-lg border border-gray-700">
        <FaExclamationCircle className="text-red-400 text-4xl mb-4" />
        <p className="text-gray-300 text-lg mb-2">Web game URL not available</p>
        <div className="text-gray-400 text-sm mb-4 text-center">
          <p>Game marked as web game: {game.is_web_game ? 'Yes' : 'No'}</p>
          <p>Has embedded version: {game.has_embedded_version ? 'Yes' : 'No'}</p>
          <p>Web game URL: {game.web_game_url || 'Not set'}</p>
          <p>Files available: {game.files?.length || 0}</p>
          {game.files?.length > 0 && (
            <p>Web playable files: {game.files?.filter(f => f.is_web_playable).length || 0}</p>
          )}
        </div>
        <details className="text-gray-500 text-xs">
          <summary className="cursor-pointer mb-2">Debug Information</summary>
          <pre className="font-mono bg-gray-900 p-2 rounded max-w-full overflow-auto">
            {JSON.stringify(debugInfo, null, 2)}
          </pre>
        </details>
      </div>
    );
  }
  
  return (
    <div className="relative w-full">
      <div
        id={`game-container-${game.id}`}
        className={`relative w-full bg-gray-900 rounded-lg overflow-hidden border border-gray-700 ${
          isFullscreen
            ? 'h-screen'
            : 'h-[50vh] min-h-[400px] max-h-[600px] lg:h-[60vh] lg:min-h-[500px] lg:max-h-[700px]'
        }`}
      >
        {!gameStarted && (
          <div className="absolute inset-0 flex flex-col items-center justify-center bg-gradient-to-br from-gray-800 to-gray-900 z-20">
            <div className="text-center">
              <div className="text-orange-400 text-6xl mb-6">
                <FaGamepad />
              </div>
              <h3 className="text-white text-2xl font-bold mb-4">Ready to Play</h3>
              <p className="text-gray-300 mb-8 max-w-md">
                Click the button below to start playing {game.title} directly in your browser
              </p>
              <button
                onClick={handlePlayClick}
                className="bg-gradient-to-r from-red-500 to-orange-500 hover:from-red-600 hover:to-orange-600 text-white px-8 py-4 rounded-lg font-bold text-xl transition-all duration-200 transform hover:scale-105 shadow-lg flex items-center gap-3 mx-auto"
              >
                <FaPlayCircle className="text-2xl" />
                Play Game
              </button>
            </div>
          </div>
        )}

        {gameStarted && isLoading && (
          <div className="absolute inset-0 flex flex-col items-center justify-center bg-gray-800 z-10">
            <LoadingSpinner size="md" color="secondary" showText={true} text="Loading web game..." />
          </div>
        )}

        {gameStarted && error && (
          <div className="absolute inset-0 flex flex-col items-center justify-center bg-gray-800 z-10 p-4">
            <FaExclamationCircle className="text-red-400 text-4xl mb-4" />
            <p className="text-red-400 text-lg mb-2">{error}</p>
            <p className="text-gray-500 text-sm mb-4">Attempted URL: {embedUrl}</p>
            <a
              href={embedUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="bg-gradient-to-r from-red-500 to-orange-500 hover:from-red-600 hover:to-orange-600 text-white px-4 py-2 rounded-lg transition-all duration-200 flex items-center gap-2"
            >
              <FaGamepad />
              Play in new window
            </a>
          </div>
        )}

        {gameStarted && (
          <iframe
            src={embedUrl}
            className="w-full h-full border-0"
            onLoad={handleLoad}
            onError={handleError}
            allow="fullscreen; autoplay; gamepad; keyboard; microphone; camera; midi; encrypted-media"
            title={`Play ${game.title}`}
            sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-pointer-lock allow-modals"
          ></iframe>
        )}

        {/* ESC Exit Message Overlay */}
        {showExitMessage && isFullscreen && (
          <div className="absolute top-4 left-1/2 transform -translate-x-1/2 z-50 bg-black bg-opacity-80 text-white px-6 py-3 rounded-lg shadow-lg backdrop-blur-sm">
            <p className="text-lg font-semibold text-center">
              {t('game.fullscreen.exitMessage')}
            </p>
          </div>
        )}

        {/* Fullscreen Toggle Button */}
        {gameStarted && !isLoading && !error && (
          <button
            onClick={toggleFullscreen}
            className="absolute top-4 right-4 z-40 bg-black bg-opacity-60 hover:bg-opacity-80 text-white p-3 rounded-lg transition-all duration-200 flex items-center gap-2 backdrop-blur-sm"
            title={isFullscreen ? t('game.fullscreen.exitFullscreen') : t('game.fullscreen.enterFullscreen')}
          >
            {isFullscreen ? <FaCompress className="text-lg" /> : <FaExpand className="text-lg" />}
          </button>
        )}
      </div>
    </div>
  );
};

WebGameEmbed.propTypes = {
  game: PropTypes.shape({
    id: PropTypes.number,
    title: PropTypes.string,
    is_web_game: PropTypes.bool,
    web_game_url: PropTypes.string,
    has_embedded_version: PropTypes.bool,
    files: PropTypes.array
  }).isRequired,
  onError: PropTypes.func
};

// Star rating component
const StarRating = ({ rating, setRating, interactive = false }) => {
  const handleClick = (selectedRating) => {
    if (interactive && setRating) {
      setRating(selectedRating);
    }
  };

  return (
    <div className="flex items-center gap-1">
      {[...Array(5)].map((_, index) => {
        const starValue = index + 1;
        return (
          <span 
            key={index} 
            className={`text-lg transition-colors duration-200 ${
              starValue <= rating ? 'text-yellow-400' : 'text-gray-600'
            } ${interactive ? 'cursor-pointer hover:text-yellow-300' : ''}`}
            onClick={() => handleClick(starValue)}
          >
            <FaStar />
          </span>
        );
      })}
      {rating > 0 && <span className="text-gray-400 text-sm ml-2">{rating} of 5</span>}
    </div>
  );
};

StarRating.propTypes = {
  rating: PropTypes.number.isRequired,
  setRating: PropTypes.func,
  interactive: PropTypes.bool
};

// ReviewComment component
const ReviewComment = ({ comment, navigate }) => {
  // Add function to navigate to user profile
  const navigateToUserProfile = (e) => {
    e.preventDefault();
    if (comment.userId) {
      navigate(`/user/${comment.userId}`);
    }
  };

  return (
    <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
      <div className="flex items-start gap-3 mb-3">
        <img 
          src={comment.avatar || avatarPlaceholder} 
          alt={comment.username} 
          className="w-8 h-8 rounded-full object-cover cursor-pointer hover:ring-2 hover:ring-orange-500 transition-all duration-200"
          onError={(e) => {
            e.target.onerror = null;
            e.target.src = avatarPlaceholder;
          }}
          onClick={navigateToUserProfile}
        />
        <div className="flex-1">
          <span 
            className="text-orange-400 font-medium cursor-pointer hover:text-orange-300 transition-colors duration-200"
            onClick={navigateToUserProfile}
          >
            {comment.username}
          </span>
          <span className="text-gray-500 text-sm ml-2">{comment.formattedDate}</span>
        </div>
      </div>
      <p className="text-gray-300 leading-relaxed">{comment.content}</p>
    </div>
  );
};

ReviewComment.propTypes = {
  comment: PropTypes.shape({
    userId: PropTypes.number,
    avatar: PropTypes.string,
    username: PropTypes.string.isRequired,
    formattedDate: PropTypes.string.isRequired,
    content: PropTypes.string.isRequired
  }).isRequired,
  navigate: PropTypes.func.isRequired
};

// ReviewCard component with improved error handling for images
const ReviewCard = ({ review, featured, onDelete }) => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [reaction, setReaction] = useState(review.userReaction || 'none');
  const [likes, setLikes] = useState(parseInt(review.likesCount || 0));
  const [dislikes, setDislikes] = useState(parseInt(review.dislikesCount || 0));
  const [reactionLoading, setReactionLoading] = useState(false);
  const [showComments, setShowComments] = useState(false);
  const [comments, setComments] = useState([]);
  const [commentsLoading, setCommentsLoading] = useState(false);
  const [commentContent, setCommentContent] = useState('');
  const [submittingComment, setSubmittingComment] = useState(false);
  const [commentCount, setCommentCount] = useState(parseInt(review.commentCount || 0));
  

  
  // Check if user has already reacted to this review when component mounts
  useEffect(() => {
    if (review.userReaction && user) {
      setReaction(review.userReaction);
    }
  }, [review.userReaction, user]);
  
  // Fetch comments when comments section is opened
  useEffect(() => {
    if (showComments && comments.length === 0) {
      fetchComments();
    }
  }, [showComments]);
  
  const fetchComments = async () => {
    try {
      setCommentsLoading(true);
      const fetchedComments = await getReviewComments(review.id);
      setComments(fetchedComments);
      
      // Update comment count only if the returned count is different
      if (fetchedComments.length !== commentCount) {
        setCommentCount(fetchedComments.length);
      }
    } catch (error) {
      console.error('Error fetching comments:', error);
      // Could show an error message here
    } finally {
      setCommentsLoading(false);
    }
  };
  
  const handleReaction = async (type) => {
    // If not logged in, redirect to login
    if (!user) {
      // Use the parent component's redirectToLogin function
      // Since we don't have direct access, we'll use window.location in subdomain mode
      if (window.location.hostname.includes('.indierepo.com') || window.location.hostname === 'indierepo.com') {
        const protocol = window.location.protocol;
        const port = window.location.port ? `:${window.location.port}` : '';
        // Redirect without returnTo parameter
        window.location.href = `${protocol}//indierepo.com${port}/login`;
      } else {
        navigate('/login', { state: { from: `/game/${review.gameId}` } });
      }
      return;
    }
    
    let newReaction = type;
    let newLikes = likes;
    let newDislikes = dislikes;
    
    // Determine the new reaction state and counts
    if (type === reaction) {
      // Removing the current reaction
      newReaction = 'none';
      if (type === 'like') {
        newLikes -= 1;
      } else {
        newDislikes -= 1;
      }
    } 
    else if (reaction !== 'none') {
      // Switching from one reaction to another
      if (type === 'like') {
        newDislikes -= 1;
        newLikes += 1;
      } else {
        newLikes -= 1;
        newDislikes += 1;
      }
    } 
    else {
      // Adding a new reaction
      if (type === 'like') {
        newLikes += 1;
      } else {
        newDislikes += 1;
      }
    }
    
    // Update state optimistically
    setReaction(newReaction);
    setLikes(newLikes);
    setDislikes(newDislikes);
    
    // Send the reaction to the server
    try {
      setReactionLoading(true);
      const result = await sendReviewReaction(review.id, newReaction);
      
      // Update counts with server response to ensure accuracy
      if (result && result.likesCount !== undefined && result.dislikesCount !== undefined) {
        setLikes(result.likesCount);
        setDislikes(result.dislikesCount);
      }
    } catch (error) {
      // Revert to previous state on error
      console.error('Error sending reaction:', error);
      setReaction(reaction);
      setLikes(parseInt(review.likesCount || 0));
      setDislikes(parseInt(review.dislikesCount || 0));
      // You could also show an error notification here
    } finally {
      setReactionLoading(false);
    }
  };

  // Add a function to navigate to user profile
  const navigateToUserProfile = (e) => {
    e.preventDefault();
    // Assuming userId is available in review object
    if (review.userId) {
      navigate(`/user/${review.userId}`);
    }
  };
  
  const toggleComments = () => {
    setShowComments(!showComments);
  };
  
  const handleCommentChange = (e) => {
    setCommentContent(e.target.value);
  };
  
  const handleCommentSubmit = async (e) => {
    e.preventDefault();
    
    // If not logged in, redirect to login
    if (!user) {
      // Use the same logic as in handleReaction
      if (window.location.hostname.includes('.indierepo.com') || window.location.hostname === 'indierepo.com') {
        const protocol = window.location.protocol;
        const port = window.location.port ? `:${window.location.port}` : '';
        // Redirect without returnTo parameter
        window.location.href = `${protocol}//indierepo.com${port}/login`;
      } else {
        navigate('/login', { state: { from: `/game/${review.gameId}` } });
      }
      return;
    }
    
    if (!commentContent.trim()) return;
    
    try {
      setSubmittingComment(true);
      
      const result = await submitReviewComment(review.id, commentContent);
      
      // Add the new comment to the comments array
      if (result && result.comment) {
        setComments([...comments, result.comment]);
        setCommentContent('');
        setCommentCount(prevCount => prevCount + 1);
      } else {
        alert('Error submitting comment: No comment data returned');
      }
    } catch (error) {
      console.error('Error submitting comment:', error);
      // Show error message to user
      alert(`Error submitting comment: ${error.response?.data?.message || error.message}`);
    } finally {
      setSubmittingComment(false);
    }
  };

  // Check if the current user is the author of this review
  const isOwnReview = user && user.id === review.userId;

  return (
    <div className={`bg-gray-800 rounded-lg p-6 border border-gray-700 ${featured ? 'ring-2 ring-orange-500 bg-gradient-to-br from-gray-800 to-gray-900' : ''}`}>
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-start gap-3">
          <img 
            src={review.avatar || avatarPlaceholder} 
            alt={review.user} 
            className="w-12 h-12 rounded-full object-cover cursor-pointer hover:ring-2 hover:ring-orange-500 transition-all duration-200" 
            onError={(e) => {
              e.target.onerror = null;
              e.target.src = avatarPlaceholder;
            }}
            onClick={navigateToUserProfile}
          />
          <div>
            <h4 
              className="text-orange-400 font-semibold cursor-pointer hover:text-orange-300 transition-colors duration-200" 
              onClick={navigateToUserProfile}
            >
              {review.user}
            </h4>
            <div className="flex items-center gap-3 mt-1">
              <span className="text-gray-500 text-sm">{review.date}</span>
              <StarRating rating={review.rating} />
            </div>
          </div>
        </div>
        
        {/* Add delete button if user owns this review */}
        {isOwnReview && (
          <button 
            onClick={() => onDelete(review.id)}
            className="flex items-center gap-2 px-3 py-1 bg-red-600 hover:bg-red-700 text-white text-sm rounded-lg transition-colors duration-200"
            title="Delete your review"
          >
            <FaTrash className="text-xs" /> Delete
          </button>
        )}
      </div>
      
      <h3 className="text-white text-xl font-bold mb-3">{review.title}</h3>
      <p className="text-gray-300 leading-relaxed mb-4">{review.content}</p>
      
      <div className="flex items-center gap-4 mb-4">
        <button 
          className={`flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200 ${
            reaction === 'like' 
              ? 'bg-green-600 text-white' 
              : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
          } ${reactionLoading ? 'opacity-50 cursor-not-allowed' : ''}`} 
          onClick={() => !reactionLoading && handleReaction('like')}
          aria-label="Like this review"
          disabled={reactionLoading}
        >
          <FaThumbsUp className="text-sm" /> {likes}
        </button>
        <button 
          className={`flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200 ${
            reaction === 'dislike' 
              ? 'bg-red-600 text-white' 
              : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
          } ${reactionLoading ? 'opacity-50 cursor-not-allowed' : ''}`} 
          onClick={() => !reactionLoading && handleReaction('dislike')}
          aria-label="Dislike this review"
          disabled={reactionLoading}
        >
          <FaThumbsDown className="text-sm" /> {dislikes}
        </button>
        <button 
          className={`flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200 ${
            showComments 
              ? 'bg-blue-600 text-white' 
              : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
          }`}
          onClick={toggleComments}
          aria-label={showComments ? 'Hide comments' : 'Show comments'}
        >
          <FaComment className="text-sm" /> {commentCount}
        </button>
      </div>
      
      {/* Comments section */}
      {showComments && (
        <div className="border-t border-gray-700 pt-4">
          <h4 className="text-white font-semibold mb-4">Comments</h4>
          
          {/* Comment form */}
          <form className="mb-4" onSubmit={handleCommentSubmit}>
            <div className="flex items-start gap-3">
              <img 
                src={(user && user.profileImage) || avatarPlaceholder} 
                alt="Your avatar" 
                className="w-8 h-8 rounded-full object-cover"
              />
              <div className="flex-1 flex gap-2">
                <input
                  type="text"
                  value={commentContent}
                  onChange={handleCommentChange}
                  placeholder={user ? "Add a comment..." : "Login to comment"}
                  className="flex-1 bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  disabled={!user || submittingComment}
                />
                <button 
                  type="submit" 
                  className="bg-gradient-to-r from-red-500 to-orange-500 hover:from-red-600 hover:to-orange-600 disabled:from-gray-600 disabled:to-gray-600 text-white px-4 py-2 rounded-lg transition-all duration-200 disabled:cursor-not-allowed"
                  disabled={!user || !commentContent.trim() || submittingComment}
                >
                  Send
                </button>
              </div>
            </div>
          </form>
          
          {/* Loading state */}
          {commentsLoading ? (
            <div className="flex items-center justify-center py-4">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-orange-500 mr-3"></div>
              <p className="text-gray-400">Loading comments...</p>
            </div>
          ) : (
            /* Comments list */
            <div className="space-y-3">
              {comments.length > 0 ? (
                comments.map(comment => (
                  <ReviewComment 
                    key={comment.id} 
                    comment={comment} 
                    navigate={navigate}  // Pass navigate function to ReviewComment
                  />
                ))
              ) : (
                <p className="text-gray-500 text-center py-4">No comments yet. Be the first to comment!</p>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

ReviewCard.propTypes = {
  review: PropTypes.shape({
    id: PropTypes.number.isRequired,
    userReaction: PropTypes.string,
    likesCount: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    dislikesCount: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    commentCount: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    gameId: PropTypes.number,
    userId: PropTypes.number,
    avatar: PropTypes.string,
    user: PropTypes.string.isRequired,
    date: PropTypes.string.isRequired,
    rating: PropTypes.number.isRequired,
    title: PropTypes.string.isRequired,
    content: PropTypes.string.isRequired
  }).isRequired,
  featured: PropTypes.bool,
  onDelete: PropTypes.func.isRequired
};

// MediaModal component for viewing screenshots and videos in fullscreen
const MediaModal = ({ isOpen, onClose, media, currentIndex, setCurrentIndex, type }) => {
  if (!isOpen) return null;

  const handlePrevious = (e) => {
    e.stopPropagation();
    setCurrentIndex((prev) => (prev > 0 ? prev - 1 : media.length - 1));
  };

  const handleNext = (e) => {
    e.stopPropagation();
    setCurrentIndex((prev) => (prev < media.length - 1 ? prev + 1 : 0));
  };

  // Extract video ID from YouTube or Vimeo URL
  const getEmbedUrl = (url) => {
    if (!url) return null;
    
    let videoId = '';
    let embedUrl = '';
    
    if (url.includes('youtube.com') || url.includes('youtu.be')) {
      const match = url.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&]+)/);
      if (match && match[1]) {
        videoId = match[1];
        embedUrl = `https://www.youtube.com/embed/${videoId}?autoplay=1`;
      }
    } else if (url.includes('vimeo.com')) {
      const match = url.match(/vimeo\.com\/(\d+)/);
      if (match && match[1]) {
        videoId = match[1];
        embedUrl = `https://player.vimeo.com/video/${videoId}?autoplay=1`;
      }
    }
    
    return embedUrl;
  };

  const currentMedia = media[currentIndex];
  
  return (
    <div className="fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-50" onClick={onClose}>
      <div className="relative max-w-6xl max-h-full w-full h-full flex items-center justify-center p-4" onClick={(e) => e.stopPropagation()}>
        <button 
          className="absolute top-4 right-4 z-10 bg-gray-800 hover:bg-gray-700 text-white p-2 rounded-full transition-colors duration-200" 
          onClick={onClose}
        >
          <FaTimes className="text-xl" />
        </button>
        
        <div className="flex items-center justify-center w-full h-full">
          <button 
            className="absolute left-4 z-10 bg-gray-800 hover:bg-gray-700 text-white p-3 rounded-full transition-colors duration-200 text-2xl"
            onClick={handlePrevious}
          >
            &lt;
          </button>
          
          <div className="flex items-center justify-center max-w-full max-h-full">
            {type === 'image' ? (
              <img 
                src={currentMedia || screenshotPlaceholder} 
                alt={`Media ${currentIndex + 1}`} 
                className="max-w-full max-h-full object-contain rounded-lg"
                onError={(e) => {
                  e.target.onerror = null; 
                  e.target.src = screenshotPlaceholder;
                }}
              />
            ) : (
              <div className="w-full h-full max-w-4xl max-h-96 lg:max-h-[70vh]">
                <iframe
                  src={getEmbedUrl(currentMedia)}
                  title={`Video ${currentIndex + 1}`}
                  frameBorder="0"
                  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                  allowFullScreen
                  className="w-full h-full rounded-lg"
                ></iframe>
              </div>
            )}
          </div>
          
          <button 
            className="absolute right-4 z-10 bg-gray-800 hover:bg-gray-700 text-white p-3 rounded-full transition-colors duration-200 text-2xl"
            onClick={handleNext}
          >
            &gt;
          </button>
        </div>
        
        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white px-4 py-2 rounded-lg">
          {currentIndex + 1} / {media.length}
        </div>
      </div>
    </div>
  );
};

MediaModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  media: PropTypes.array.isRequired,
  currentIndex: PropTypes.number.isRequired,
  setCurrentIndex: PropTypes.func.isRequired,
  type: PropTypes.string.isRequired
};

// Modified VideoEmbed component to support thumbnail display
const VideoEmbed = ({ url, isMain = false, onClick = null, thumbnail = false }) => {
  // Extract video ID from YouTube or Vimeo URL
  let videoId = '';
  let platform = '';
  let thumbnailUrl = '';
  
  if (!url) return null;
  
  if (url.includes('youtube.com') || url.includes('youtu.be')) {
    platform = 'youtube';
    const match = url.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&]+)/);
    if (match && match[1]) {
      videoId = match[1];
      thumbnailUrl = `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`;
    }
  } else if (url.includes('vimeo.com')) {
    platform = 'vimeo';
    const match = url.match(/vimeo\.com\/(\d+)/);
    if (match && match[1]) {
      videoId = match[1];
      // Note: Vimeo doesn't have a simple thumbnail URL pattern like YouTube
      // For a real app, you'd need to use Vimeo's API to fetch the thumbnail
      thumbnailUrl = ''; // Placeholder for Vimeo thumbnail
    }
  }
  
  if (!videoId) return null;
  
  // For thumbnail display
  if (thumbnail) {
    return (
      <div 
        className="relative bg-gray-800 rounded-lg overflow-hidden cursor-pointer group hover:ring-2 hover:ring-orange-500 transition-all duration-200 aspect-video bg-cover bg-center"
        onClick={onClick}
        style={{ backgroundImage: `url(${thumbnailUrl || screenshotPlaceholder})` }}
      >
        <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center group-hover:bg-opacity-20 transition-all duration-200">
          <FaPlayCircle className="text-white text-6xl opacity-80 group-hover:opacity-100 group-hover:scale-110 transition-all duration-200" />
        </div>
      </div>
    );
  }
  
  // Prepare embed URL for actual iframe embed
  let embedUrl = '';
  if (platform === 'youtube') {
    embedUrl = `https://www.youtube.com/embed/${videoId}`;
  } else if (platform === 'vimeo') {
    embedUrl = `https://player.vimeo.com/video/${videoId}`;
  }
  
  return (
    <div className={`w-full rounded-lg overflow-hidden bg-gray-900 ${isMain ? 'aspect-video' : 'aspect-video'}`}>
      <iframe
        src={embedUrl}
        title={isMain ? "Main Game Video" : "Additional Game Video"}
        frameBorder="0"
        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
        allowFullScreen
        className="w-full h-full"
      ></iframe>
    </div>
  );
};

VideoEmbed.propTypes = {
  url: PropTypes.string,
  isMain: PropTypes.bool,
  onClick: PropTypes.func,
  thumbnail: PropTypes.bool
};

// CollapsibleSection component
const CollapsibleSection = ({ title, children, initialOpen = false }) => {
  const [isOpen, setIsOpen] = useState(initialOpen);
  
  return (
    <div className="bg-gray-800 rounded-lg border border-gray-700 overflow-hidden">
      <div 
        className="flex items-center justify-between p-4 cursor-pointer hover:bg-gray-700 transition-colors duration-200" 
        onClick={() => setIsOpen(!isOpen)}
      >
        <h3 className="text-white text-lg font-semibold">{title}</h3>
        <span className="text-orange-400 text-xl">
          {isOpen ? <FaChevronUp /> : <FaChevronDown />}
        </span>
      </div>
      <div className={`transition-all duration-300 ease-in-out ${isOpen ? 'max-h-none opacity-100' : 'max-h-0 opacity-0 overflow-hidden'}`}>
        <div className="p-4 pt-0">
          {children}
        </div>
      </div>
    </div>
  );
};

CollapsibleSection.propTypes = {
  title: PropTypes.string.isRequired,
  children: PropTypes.node.isRequired,
  initialOpen: PropTypes.bool
};

// Add a GameFile component to display download options
const GameFile = ({ file, gameData, onDownload, onExternalDownload, onPurchaseClick }) => {
  const isPaid = gameData.priceModel !== 'free';
  const isLocked = file.requiresPurchase && isPaid;
  const hasPurchased = gameData.userHasPurchased;
  
  // Helper function to get the appropriate icon based on file type
  const getFileIcon = (type) => {
    switch (type) {
      case 'game': return <FaGamepad />;
      case 'demo': return <FaGamepad />;
      case 'mod': return <FaTools />;
      case 'tools': return <FaTools />;
      default: return <FaFileAlt />;
    }
  };

  // Format file size nicely
  const formatFileSize = (sizeInKB) => {
    if (!sizeInKB) return '';
    
    if (sizeInKB < 1000) {
      return `${sizeInKB} KB`;
    } else {
      return `${(sizeInKB / 1024).toFixed(1)} MB`;
    }
  };

  const handleClick = () => {
    if (isLocked) return; // Do nothing for locked files
    
    if (file.isExternalLink) {
      onExternalDownload(file);
    } else {
      onDownload(file);
    }
  };

  return (
    <div className={`flex items-center justify-between p-4 bg-gray-800 rounded-lg border border-gray-700 ${isLocked ? 'opacity-75' : ''}`}>
      <div className="flex items-center gap-4 flex-1">
        <div className="text-orange-400 text-2xl">
          {file.isExternalLink ? <FaLink /> : getFileIcon(file.fileType)}
        </div>
        <div className="flex-1">
          <h4 className="text-white font-semibold flex items-center gap-2 mb-1">
            {file.fileName}
            {file.fileType === 'demo' && <span className="bg-blue-600 text-white text-xs px-2 py-1 rounded">Demo</span>}
            {file.fileType === 'mod' && <span className="bg-purple-600 text-white text-xs px-2 py-1 rounded">Mod</span>}
            {file.isExternalLink && <span className="bg-green-600 text-white text-xs px-2 py-1 rounded">External</span>}
          </h4>
          {file.description && <p className="text-gray-400 text-sm mb-1">{file.description}</p>}
          {file.fileSize > 0 && <span className="text-gray-500 text-xs">{formatFileSize(file.fileSize)}</span>}
        </div>
      </div>
      
      <div className="ml-4">
        {isLocked && !hasPurchased ? (
          <button
            className="flex items-center gap-2 bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg transition-colors duration-200"
            onClick={onPurchaseClick}
            title="Purchase to unlock this file"
          >
            <FaLock className="text-sm" /> Purchase to Unlock
          </button>
        ) : (
          <button
            onClick={handleClick}
            className="flex items-center gap-2 bg-gradient-to-r from-red-500 to-orange-500 hover:from-red-600 hover:to-orange-600 text-white px-4 py-2 rounded-lg transition-all duration-200"
            title={file.isExternalLink ? "Go to external download" : "Download this file"}
          >
            {file.isExternalLink ? <FaLink className="text-sm" /> : <FaDownload className="text-sm" />}
            {file.isExternalLink ? "Download Link" : "Download"}
          </button>
        )}
      </div>
    </div>
  );
};

GameFile.propTypes = {
  file: PropTypes.shape({
    requiresPurchase: PropTypes.bool,
    isExternalLink: PropTypes.bool,
    fileName: PropTypes.string,
    fileType: PropTypes.string,
    description: PropTypes.string,
    fileSize: PropTypes.number,
    externalUrl: PropTypes.string
  }).isRequired,
  gameData: PropTypes.shape({
    priceModel: PropTypes.string,
    userHasPurchased: PropTypes.bool
  }).isRequired,
  onDownload: PropTypes.func.isRequired,
  onExternalDownload: PropTypes.func.isRequired,
  onPurchaseClick: PropTypes.func.isRequired
};

const GamePage = ({ usePath = false }) => {
  const params = useParams();
  const navigate = useNavigate();
  const { user } = useAuth();
  const { t } = useLanguage();
  const [game, setGame] = useState(null);
  const [reviews, setReviews] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [submitting, setSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [submitError, setSubmitError] = useState('');
  const [newReviewData, setNewReviewData] = useState({
    title: '',
    content: '',
    rating: 0
  });
  const [lastAction, setLastAction] = useState(''); // Add this new state variable
  const [avgRating, setAvgRating] = useState(0);
  const [reviewCount, setReviewCount] = useState(0);
  const [purchaseStatus, setPurchaseStatus] = useState({
    inProgress: false,
    success: false,
    error: null
  });
  
  // Media modal state
  const [modalOpen, setModalOpen] = useState(false);
  const [currentMediaType, setCurrentMediaType] = useState('image'); // 'image' or 'video'
  const [currentMedia, setCurrentMedia] = useState([]);
  const [currentMediaIndex, setCurrentMediaIndex] = useState(0);
  
  // Add state for delete confirmation
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [reviewToDelete, setReviewToDelete] = useState(null);
  
  // Add state for purchase modal
  const [isPurchaseModalOpen, setIsPurchaseModalOpen] = useState(false);
  
  // Extract the game identifier: use the slug from params if path-based, or use the id from params
  const slug = usePath ? params.gameSlug : null;
  const gameId = params.id;
  
  // Add this state to track failed image URLs
  const [failedImageUrls, setFailedImageUrls] = useState(new Set());
  
  // Fetch game data
  useEffect(() => {
    const fetchGameData = async () => {
      try {
        setLoading(true);
        
        let gameData;
        let fetchedGameId;
        
        // Log the access method

        
        // Fetch game details - by slug if in path mode, otherwise by ID
        if (usePath && slug) {
          try {
            gameData = await getGameBySlug(slug);
            fetchedGameId = gameData.id;
          } catch (err) {
            if (err.response?.status === 404) {
              // Custom error for slug not matching a game
              throw new Error(`No game found for "${slug}". This may be a test or non-existent game.`);
            }
            throw err;
          }
        } else {
          gameData = await getGameById(gameId);
          fetchedGameId = gameId;
        }
        
        setGame(gameData);
        
        try {
          // Fetch game reviews with user reactions if logged in (using the actual game ID)
          const reviewsData = await getGameReviews(fetchedGameId);
          setReviews(reviewsData);
        } catch (reviewError) {
          console.error('Error fetching reviews:', reviewError);
          // Continue execution even if reviews fail to load
          setReviews([]);
        }
        
        setLoading(false);
      } catch (err) {
        console.error('Error fetching game data:', err);
        setError(err.message || 'Failed to load game data');
        setLoading(false);
      }
    };
    
    fetchGameData();
  }, [gameId, slug, usePath]);  
  
  // Reset success message after timeout
  useEffect(() => {
    if (submitSuccess) {
      const timer = setTimeout(() => {
        setSubmitSuccess(false);
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [submitSuccess]);

  // Calculate average rating when reviews change
  useEffect(() => {
    if (reviews && reviews.length > 0) {
      const totalRating = reviews.reduce((sum, review) => sum + review.rating, 0);
      const average = totalRating / reviews.length;
      setAvgRating(parseFloat(average.toFixed(1))); // Round to 1 decimal place
      setReviewCount(reviews.length);
    } else {
      setAvgRating(0);
      setReviewCount(0);
    }
  }, [reviews]);
  
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setNewReviewData({
      ...newReviewData,
      [name]: value
    });
  };
  
  const handleRatingChange = (newRating) => {
    setNewReviewData(prev => ({
      ...prev,
      rating: newRating
    }));
  };
  
  // Add this function to handle login redirects consistently
  const redirectToLogin = (returnPath) => {
    // Always use React Router navigation since we're no longer using subdomains
    navigate('/login', { state: { from: returnPath } });
  };

  const handleSubmitReview = async (e) => {
    e.preventDefault();
    
    if (!user) {
      // Use the new redirectToLogin function
      redirectToLogin(`/game/${game.id}`);
      return;
    }
    
    // Reset submission states
    setSubmitting(true);
    setSubmitError('');
    
    try {
      const reviewToSubmit = {
        title: newReviewData.title || 'Review',
        comment: newReviewData.content,
        rating: newReviewData.rating,
        userId: user.id,
      };
      
      await submitGameReview(game.id, reviewToSubmit);
      
      // Refresh reviews after submission
      const updatedReviews = await getGameReviews(game.id);
      setReviews(updatedReviews);
      
      // Show success message with action type
      setLastAction('submitted');
      setSubmitSuccess(true);
      
      // Clear the form
      setNewReviewData({
        title: '',
        content: '',
        rating: 0
      });
    } catch (error) {
      console.error('Failed to submit review:', error);
      
      // Check if this is a duplicate review error
      if (error.response?.status === 400 && error.response?.data?.message?.includes('already reviewed')) {
        setSubmitError('You have already submitted a review for this game. Please edit or delete your existing review.');
      } else {
        setSubmitError('Failed to submit your review. Please try again.');
      }
    } finally {
      setSubmitting(false);
    }
  };

  // Add a state to track if we should fallback to cover image if web game fails
  const [webGameError, setWebGameError] = useState(false);

  // Game interaction states
  const [gameInteractions, setGameInteractions] = useState({
    likes: 0,
    dislikes: 0,
    isFavorite: false,
    userLiked: false,
    userDisliked: false
  });
  const [interactionLoading, setInteractionLoading] = useState(false);

  // Game interaction handlers
  const handleLikeGame = async () => {
    if (!user) {
      redirectToLogin(`/game/${game.id}`);
      return;
    }

    if (interactionLoading) return;

    try {
      setInteractionLoading(true);
      const result = await likeGame(game.id);
      setGameInteractions(prev => ({
        ...prev,
        likes: result.likes,
        dislikes: result.dislikes,
        userLiked: result.userLiked,
        userDisliked: result.userDisliked
      }));
    } catch (error) {
      console.error('Error liking game:', error);
    } finally {
      setInteractionLoading(false);
    }
  };

  const handleDislikeGame = async () => {
    if (!user) {
      redirectToLogin(`/game/${game.id}`);
      return;
    }

    if (interactionLoading) return;

    try {
      setInteractionLoading(true);
      const result = await dislikeGame(game.id);
      setGameInteractions(prev => ({
        ...prev,
        likes: result.likes,
        dislikes: result.dislikes,
        userLiked: result.userLiked,
        userDisliked: result.userDisliked
      }));
    } catch (error) {
      console.error('Error disliking game:', error);
    } finally {
      setInteractionLoading(false);
    }
  };

  const handleToggleFavorite = async () => {
    if (!user) {
      redirectToLogin(`/game/${game.id}`);
      return;
    }

    if (interactionLoading) return;

    try {
      setInteractionLoading(true);
      const result = await toggleFavoriteGame(game.id);
      setGameInteractions(prev => ({
        ...prev,
        isFavorite: result.isFavorite
      }));
    } catch (error) {
      console.error('Error toggling favorite:', error);
    } finally {
      setInteractionLoading(false);
    }
  };

  // Load game interactions when game loads
  useEffect(() => {
    const loadGameInteractions = async () => {
      if (game && game.id) {
        try {
          const interactions = await getGameInteractions(game.id);
          setGameInteractions(interactions);
        } catch (error) {
          console.error('Error loading game interactions:', error);
          // Set default values if loading fails
          setGameInteractions({
            likes: 0,
            dislikes: 0,
            isFavorite: false,
            userLiked: false,
            userDisliked: false
          });
        }
      }
    };

    loadGameInteractions();
  }, [game]);

  // Add a handler for external downloads
  const handleExternalDownload = (file) => {
    window.open(file.externalUrl, '_blank');
  };
  
  const handleDownload = (file) => {
    // Use the file's actual path
    if (file.filePath) {
      const baseUrl = API_URL.replace('/api', ''); // Remove /api suffix for direct file access
      window.open(`${baseUrl}${file.filePath}`, '_blank');
    }
  };

  // Open media modal with appropriate content
  const openMediaModal = (type, index) => {
    if (type === 'image' && game.screenshots && game.screenshots.length > 0) {
      setCurrentMediaType('image');
      setCurrentMedia(game.screenshots);
      setCurrentMediaIndex(index);
      setModalOpen(true);
    } else if (type === 'video' && game.additionalVideos && game.additionalVideos.length > 0) {
      setCurrentMediaType('video');
      setCurrentMedia(game.additionalVideos.filter(url => url)); // Filter out empty URLs
      setCurrentMediaIndex(index);
      setModalOpen(true);
    }
  };

  // Helper function to get the correct image URL
  const getCoverImageUrl = () => {
    // Helper function to build full URL from path
    const buildFullUrl = (path) => {
      if (!path) return null;
      
      if (path.startsWith('http://') || path.startsWith('https://')) {
        return path; // Already a full URL
      }
      
      // Use the centralized base URL configuration
      const baseUrl = BASE_URL;
      
      if (path.startsWith('/')) {
        return `${baseUrl}${path}`; // Path has leading slash
      } else {
        return `${baseUrl}/${path}`; // Add leading slash
      }
    };
    
    // Try all possible ways to get the cover image
    
    // 1. Check for direct coverImage property
    if (game.coverImage) {
      return buildFullUrl(game.coverImage);
    }
    

    
    // 2. Check for GameImages array (SQL join result)
    if (game.GameImages && Array.isArray(game.GameImages)) {
      const coverImg = game.GameImages.find(img => img.imageType === 'cover');
      if (coverImg && coverImg.filePath) {
        return buildFullUrl(coverImg.filePath);
      }
    }
    
    // 3. Check for an images array property
    if (game.images && Array.isArray(game.images)) {
      const coverImg = game.images.find(img => img.imageType === 'cover');
      if (coverImg && coverImg.filePath) {
        return buildFullUrl(coverImg.filePath);
      }
    }
    
    // 4. Check for a generic image property
    if (game.image) {
      return buildFullUrl(game.image);
    }
    
    // 5. Try a direct path based on convention
    if (game.id) {
      return buildFullUrl(`/uploads/games/${game.id}/images/cover.jpg`);
    }
    
    // Fallback to placeholder
    return gamePlaceholder;
  };

  // Handle initial delete request - opens confirmation dialog
  const handleDeleteRequest = (reviewId) => {
    setReviewToDelete(reviewId);
    setDeleteConfirmOpen(true);
  };
  
  // Handle confirmed deletion
  const confirmDeleteReview = async () => {
    if (!reviewToDelete) return;
    
    try {
      setSubmitting(true);
      await deleteGameReview(reviewToDelete);
      
      // Update reviews list after successful deletion
      setReviews(reviews.filter(review => review.id !== reviewToDelete));
      
      // Show success message with action type
      setLastAction('deleted'); // Set the last action to 'deleted'
      setSubmitSuccess(true);
      setSubmitError('');
    } catch (error) {
      console.error('Failed to delete review:', error);
      setSubmitError('Failed to delete your review. Please try again.');
    } finally {
      setSubmitting(false);
      setDeleteConfirmOpen(false);
      setReviewToDelete(null);
    }
  };

  // Calculate featured and other reviews
  const featuredReviews = reviews.filter(review => review.featured);
  const otherReviews = reviews.filter(review => !review.featured);
  
  // Determine if we have media content to show
  const hasScreenshots = game && game.screenshots && game.screenshots.length > 0;
  const hasVideos = game && game.additionalVideos && game.additionalVideos.filter(url => url).length > 0;
  const hasMedia = hasScreenshots || hasVideos;
  const hasPlatformLinks = game && (game.steamUrl || game.itchUrl || game.epicGamesUrl);
  
  // Function to open the purchase modal
  const openPurchaseModal = () => {
    setIsPurchaseModalOpen(true);
  };
  
  // Function to close the purchase modal
  const closePurchaseModal = () => {
    // Only allow closing if not in the middle of a payment
    if (!purchaseStatus.inProgress) {
      setIsPurchaseModalOpen(false);
      
      // Reset purchase status when modal is closed after a successful payment
      if (purchaseStatus.success) {
        setPurchaseStatus({
          inProgress: false,
          success: false,
          error: null
        });
      }
    }
  };

  // Add payment success handler
  const handlePaymentSuccess = () => {
    setPurchaseStatus({
      inProgress: false,
      success: true,
      error: null
    });

    // Refresh game data to update access status
    const fetchGameData = async () => {
      try {
        let gameData;
        if (usePath && slug) {
          gameData = await getGameBySlug(slug);
        } else {
          gameData = await getGameById(gameId);
        }
        setGame(gameData);
      } catch (err) {
        console.error('Error refreshing game data:', err);
      }
    };

    fetchGameData();
  };

  // Add payment error handler
  const handlePaymentError = (errorMessage) => {
    setPurchaseStatus({
      inProgress: false,
      success: false,
      error: errorMessage || 'Payment failed'
    });
  };

  // Add payment cancel handler
  const handlePaymentCancel = () => {
    setPurchaseStatus({
      inProgress: false,
      success: false,
      error: null
    });
  };

  // Function to check if user has purchased this game
  const hasPurchased = () => {
    return game?.userHasPurchased || purchaseStatus.success;
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* Delete confirmation modal */}
      {deleteConfirmOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4 border border-gray-700">
            <h3 className="text-white text-xl font-bold mb-4">Delete Review</h3>
            <p className="text-gray-300 mb-6">Are you sure you want to delete your review? This action cannot be undone.</p>
            <div className="flex gap-3 justify-end">
              <button 
                onClick={() => setDeleteConfirmOpen(false)}
                className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors duration-200"
              >
                Cancel
              </button>
              <button 
                onClick={confirmDeleteReview}
                className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                disabled={submitting}
              >
                {submitting ? 'Deleting...' : 'Delete Review'}
              </button>
            </div>
          </div>
        </div>
      )}
      
      {/* Media modal */}
      <MediaModal 
        isOpen={modalOpen}
        onClose={() => setModalOpen(false)}
        media={currentMedia}
        currentIndex={currentMediaIndex}
        setCurrentIndex={setCurrentMediaIndex}
        type={currentMediaType}
      />
      
      {/* Back navigation */}
      <div className="p-4">
        <Link to="/" className="inline-flex items-center gap-2 text-orange-400 hover:text-orange-300 transition-colors duration-200">
          <FaArrowLeft /> Back to Home
        </Link>
      </div>
      
      {/* Main content */}
      <div className="container mx-auto px-4 pb-8">
        {loading && (
          <div className="flex flex-col items-center justify-center min-h-96">
            <LoadingSpinner size="lg" color="secondary" showText={true} text="Loading game details..." />
          </div>
        )}
        
        {error && (
          <div className="flex flex-col items-center justify-center min-h-96 text-center">
            <FaExclamationTriangle className="text-red-400 text-6xl mb-4" />
            <h2 className="text-white text-2xl font-bold mb-2">Error Loading Game</h2>
            <p className="text-gray-300 mb-6">{error}</p>
            <button 
              onClick={() => navigate('/')} 
              className="bg-gradient-to-r from-red-500 to-orange-500 hover:from-red-600 hover:to-orange-600 text-white px-6 py-3 rounded-lg transition-all duration-200"
            >
              Back to Home
            </button>
          </div>
        )}
        
        {!loading && !error && game && (
          <>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
              <div className={`${game.is_web_game && !webGameError ? 'lg:col-span-2' : ''}`}>
                {/* Conditionally show web game embed or cover image */}
                {game.is_web_game && !webGameError ? (
                  <WebGameEmbed 
                    game={game} 
                    onError={() => setWebGameError(true)}
                  />
                ) : (
                  <div className="relative">
                    <img 
                      src={getCoverImageUrl()} 
                      alt={game.title} 
                      className="w-full h-auto rounded-lg object-cover"
                      onError={(e) => {
                        e.target.onerror = null; // Important: prevent infinite loops
                        e.target.src = gamePlaceholder;
                        if (game.coverImage) {
                          setFailedImageUrls(prevFailed => new Set([...prevFailed, game.coverImage]));
                        }
                      }}
                    />
                    
                    {/* Add play button for web games when showing cover image due to error */}
                    {game.isWebGame && webGameError && (
                      <div className="absolute inset-0 flex items-center justify-center">
                        <a 
                          href={game.webGameUrl} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="bg-gradient-to-r from-red-500 to-orange-500 hover:from-red-600 hover:to-orange-600 text-white px-6 py-3 rounded-lg transition-all duration-200 flex items-center gap-2"
                        >
                          <FaGamepad /> Play in new window
                        </a>
                      </div>
                    )}
                  </div>
                )}
              </div>
              
              <div className={`${game.isWebGame && !webGameError ? 'lg:col-span-2' : ''}`}>
                {/* Show web game badge if it's a web game */}
                {game.isWebGame && (
                  <div className="inline-flex items-center gap-2 bg-gradient-to-r from-red-500 to-orange-500 text-white px-3 py-1 rounded-full text-sm font-semibold mb-4">
                    <FaGamepad /> {game.webGameType?.toUpperCase() || 'WEB'} GAME
                  </div>
                )}
                
                <h1 className="text-4xl font-bold text-white mb-6">{game.title}</h1>
                
                {/* Game metadata */}
                <div className="space-y-4 mb-6">
                  <div className="flex flex-wrap items-center gap-2">
                    <span className="text-gray-400">Published by:</span> 
                    <span className="text-orange-400 font-medium">{game.publisherName}</span>
                  </div>
                  
                  <div className="flex flex-wrap items-center gap-4">
                    <span className="text-gray-400">User Rating:</span>
                    <div className="flex items-center gap-2">
                      <StarRating rating={avgRating} />
                      {reviewCount > 0 ? (
                        <span className="text-gray-300 text-sm">
                          {avgRating} out of 5 ({reviewCount} {reviewCount === 1 ? 'review' : 'reviews'})
                        </span>
                      ) : (
                        <span className="text-gray-500 text-sm">No ratings yet</span>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex flex-wrap items-center gap-2">
                    <span className="text-gray-400">Release Date:</span> 
                    <span className="text-gray-300">{new Date(game.releaseDate).toLocaleDateString()}</span>
                  </div>
                  
                  <div className="flex flex-wrap items-center gap-2">
                    <span className="text-gray-400">Price:</span> 
                    <span className="text-green-400 font-semibold">{
                      game.priceModel === 'free' ? 'Free' :
                      game.priceModel === 'paid' ? `$${game.price}` :
                      game.priceModel === 'credits' ? `${game.creditPrice} Credits` : 'Free'
                    }</span>
                  </div>
                  
                  {hasPlatformLinks && (
                    <div className="flex flex-wrap items-center gap-2">
                      <span className="text-gray-400">Available on:</span>
                      <div className="flex gap-2">
                        {game.steamUrl && (
                          <a href={game.steamUrl} target="_blank" rel="noopener noreferrer" className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm transition-colors duration-200">
                            Steam
                          </a>
                        )}
                        {game.itchUrl && (
                          <a href={game.itchUrl} target="_blank" rel="noopener noreferrer" className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm transition-colors duration-200">
                            itch.io
                          </a>
                        )}
                        {game.epicGamesUrl && (
                          <a href={game.epicGamesUrl} target="_blank" rel="noopener noreferrer" className="bg-gray-700 hover:bg-gray-600 text-white px-3 py-1 rounded text-sm transition-colors duration-200">
                            Epic Games
                          </a>
                        )}
                      </div>
                    </div>
                  )}
                </div>
                
                <div className="mb-6">
                  <h3 className="text-xl font-semibold text-white mb-3">About</h3>
                  <p className="text-gray-300 leading-relaxed">{game.description}</p>
                </div>
                
                {game.mainVideo && (
                  <div className="mb-6">
                    <VideoEmbed url={game.mainVideo} isMain={true} />
                  </div>
                )}
              </div>
            </div>

            {/* Game Interaction Buttons */}
            <div className="bg-gray-900 border-t border-gray-700 px-6 py-3 mb-8">
              <div className="flex items-center justify-end gap-3">
                <button
                  onClick={handleLikeGame}
                  disabled={interactionLoading}
                  className={`flex items-center gap-2 px-3 py-2 rounded-md transition-all duration-200 ${
                    gameInteractions.userLiked
                      ? 'bg-green-600 text-white'
                      : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                  } ${interactionLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
                  title={t('game.interactions.like')}
                >
                  <FaThumbsUp className="text-sm" />
                  <span className="text-sm font-medium">{gameInteractions.likes}</span>
                </button>

                <button
                  onClick={handleDislikeGame}
                  disabled={interactionLoading}
                  className={`flex items-center gap-2 px-3 py-2 rounded-md transition-all duration-200 ${
                    gameInteractions.userDisliked
                      ? 'bg-red-600 text-white'
                      : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                  } ${interactionLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
                  title={t('game.interactions.dislike')}
                >
                  <FaThumbsDown className="text-sm" />
                  <span className="text-sm font-medium">{gameInteractions.dislikes}</span>
                </button>

                <button
                  onClick={handleToggleFavorite}
                  disabled={interactionLoading}
                  className={`flex items-center gap-2 px-3 py-2 rounded-md transition-all duration-200 ${
                    gameInteractions.isFavorite
                      ? 'bg-pink-600 text-white'
                      : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                  } ${interactionLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
                  title={gameInteractions.isFavorite ? t('game.interactions.removeFromFavorites') : t('game.interactions.addToFavorites')}
                >
                  <FaHeart className="text-sm" />
                </button>
              </div>
            </div>



            
            {/* Reviews section */}
            <div className="mt-12">
              <h2 className="text-3xl font-bold text-white mb-8">Reviews</h2>
              <div className="bg-gray-800 rounded-lg p-6 border border-gray-700 mb-8">
                <h3 className="text-xl font-semibold text-white mb-4">Write a Review</h3>
                {submitSuccess && (
                  <div className="bg-green-900 bg-opacity-50 border border-green-600 text-green-200 p-4 rounded-lg mb-4">
                    Your review has been {lastAction} successfully!
                  </div>
                )}
                {submitError && (
                  <div className="bg-red-900 bg-opacity-50 border border-red-600 text-red-200 p-4 rounded-lg mb-4">
                    {submitError}
                  </div>
                )}
                <form onSubmit={handleSubmitReview} className="space-y-4">
                  <div>
                    <label htmlFor="title" className="block text-white font-medium mb-2">Review Title:</label>
                    <input 
                      type="text" 
                      id="title" 
                      name="title" 
                      value={newReviewData.title} 
                      onChange={handleInputChange} 
                      placeholder="Summarize your thoughts..." 
                      className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                      required 
                    />
                  </div>
                  <div>
                    <label htmlFor="content" className="block text-white font-medium mb-2">Review:</label>
                    <textarea 
                      id="content" 
                      name="content" 
                      value={newReviewData.content} 
                      onChange={handleInputChange} 
                      placeholder="Share your experience with this game..." 
                      rows="5" 
                      className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent resize-vertical"
                      required 
                    ></textarea>
                  </div>
                  <div>
                    <label className="block text-white font-medium mb-2">Rating</label>
                    <div className="mb-4">
                      <StarRating 
                        rating={newReviewData.rating} 
                        setRating={handleRatingChange} 
                        interactive={true} 
                      />
                    </div>
                  </div>
                  <button 
                    type="submit" 
                    className={`bg-gradient-to-r from-red-500 to-orange-500 hover:from-red-600 hover:to-orange-600 disabled:from-gray-600 disabled:to-gray-600 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-200 disabled:cursor-not-allowed ${submitting ? 'opacity-75' : ''}`}
                    disabled={!newReviewData.title || !newReviewData.content || newReviewData.rating === 0 || submitting}
                  >
                    {submitting ? 'Submitting...' : user ? 'Submit Review' : 'Login to Review'}
                  </button>
                  {!user && (
                    <p className="text-gray-400 text-sm mt-2">
                      Please <span onClick={() => redirectToLogin(`/game/${game.id}`)} className="text-green-400 cursor-pointer underline hover:text-green-300">login</span> to submit a review.
                    </p>
                  )}
                </form>
              </div>
              
              {featuredReviews.length > 0 && (
                <div className="mb-8">
                  <h3 className="text-2xl font-semibold text-white mb-4">Top Reviews</h3>
                  <div className="space-y-4">
                    {featuredReviews.map(review => (
                      <ReviewCard key={review.id} review={review} featured={true} onDelete={handleDeleteRequest} />
                    ))}
                  </div>
                </div>
              )}
              
              {otherReviews.length > 0 && (
                <div className="mb-8">
                  <h3 className="text-2xl font-semibold text-white mb-4">Community Reviews</h3>
                  <div className="space-y-4">
                    {otherReviews.map(review => (
                      <ReviewCard key={review.id} review={review} featured={false} onDelete={handleDeleteRequest} />
                    ))}
                  </div>
                </div>
              )}
              
              {reviews.length === 0 && (
                <div className="text-center py-12 bg-gray-800 rounded-lg border border-gray-700">
                  <p className="text-gray-400 text-lg">No reviews yet. Be the first to review this game!</p>
                </div>
              )}
            </div>
          </>
        )}
      </div>
      
      {/* Add the PurchaseModal component */}
      <PurchaseModal 
        isOpen={isPurchaseModalOpen} 
        onClose={closePurchaseModal} 
        game={game || {}} 
        onSuccess={handlePaymentSuccess} 
        onError={handlePaymentError} 
        onCancel={handlePaymentCancel} 
        processingPayment={purchaseStatus.inProgress} 
        paymentSuccess={purchaseStatus.success} 
      />
    </div>
  );
};

GamePage.propTypes = {
  usePath: PropTypes.bool
};

export default GamePage;
